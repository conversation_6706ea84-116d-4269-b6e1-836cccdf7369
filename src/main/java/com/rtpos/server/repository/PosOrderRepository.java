package com.rtpos.server.repository;

import com.rtpos.server.entity.PosOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * POS订单数据访问层
 *
 * <AUTHOR>
 */
@Repository
public interface PosOrderRepository extends JpaRepository<PosOrder, Long> {

    /**
     * 根据业务订单ID查找订单
     */
    Optional<PosOrder> findByBizOrderId(Long bizOrderId);

    /**
     * 根据外部订单ID查找订单
     */
    Optional<PosOrder> findByOutOrderId(String outOrderId);

    /**
     * 根据门店ID查找订单（分页）
     */
    Page<PosOrder> findByStoreIdAndDeletedFalse(String storeId, Pageable pageable);

    /**
     * 根据门店ID和时间范围查找订单
     */
    @Query("SELECT p FROM PosOrder p WHERE p.storeId = :storeId " +
           "AND p.orderTime >= :startTime AND p.orderTime <= :endTime " +
           "AND p.deleted = false ORDER BY p.orderTime DESC")
    Page<PosOrder> findByStoreIdAndOrderTimeBetween(
            @Param("storeId") String storeId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            Pageable pageable);

    /**
     * 根据门店ID和时间范围查找订单（使用LocalDateTime）
     */
    @Query("SELECT p FROM PosOrder p WHERE p.storeId = :storeId " +
           "AND p.orderTimeDateTime >= :startTime AND p.orderTimeDateTime <= :endTime " +
           "AND p.deleted = false ORDER BY p.orderTimeDateTime DESC")
    Page<PosOrder> findByStoreIdAndOrderTimeDateTimeBetween(
            @Param("storeId") String storeId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            Pageable pageable);

    /**
     * 根据订单状态查找订单
     */
    Page<PosOrder> findByOrderStatusAndDeletedFalse(Integer orderStatus, Pageable pageable);

    /**
     * 根据操作员ID查找订单
     */
    Page<PosOrder> findByOperatorIdAndDeletedFalse(String operatorId, Pageable pageable);

    /**
     * 根据设备ID查找订单
     */
    Page<PosOrder> findByDeviceIdAndDeletedFalse(String deviceId, Pageable pageable);

    /**
     * 统计门店订单数量
     */
    @Query("SELECT COUNT(p) FROM PosOrder p WHERE p.storeId = :storeId AND p.deleted = false")
    Long countByStoreId(@Param("storeId") String storeId);

    /**
     * 统计门店指定时间范围内的订单数量
     */
    @Query("SELECT COUNT(p) FROM PosOrder p WHERE p.storeId = :storeId " +
           "AND p.orderTime >= :startTime AND p.orderTime <= :endTime " +
           "AND p.deleted = false")
    Long countByStoreIdAndOrderTimeBetween(
            @Param("storeId") String storeId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 统计门店指定时间范围内的销售额（原始金额）
     */
    @Query("SELECT COALESCE(SUM(p.originalAmt), 0) FROM PosOrder p WHERE p.storeId = :storeId " +
           "AND p.orderTime >= :startTime AND p.orderTime <= :endTime " +
           "AND p.orderStatus = 1 AND p.deleted = false")
    Long sumOriginalAmtByStoreIdAndOrderTimeBetween(
            @Param("storeId") String storeId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 统计门店指定时间范围内的实际销售额（折扣后金额）
     */
    @Query("SELECT COALESCE(SUM(p.discountAmt), 0) FROM PosOrder p WHERE p.storeId = :storeId " +
           "AND p.orderTime >= :startTime AND p.orderTime <= :endTime " +
           "AND p.orderStatus = 1 AND p.deleted = false")
    Long sumDiscountAmtByStoreIdAndOrderTimeBetween(
            @Param("storeId") String storeId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

    /**
     * 查找最新的订单（用于增量同步）
     */
    @Query("SELECT p FROM PosOrder p WHERE p.deleted = false ORDER BY p.gmtModified DESC")
    List<PosOrder> findLatestOrders(Pageable pageable);

    /**
     * 根据修改时间查找需要同步的订单
     */
    @Query("SELECT p FROM PosOrder p WHERE p.gmtModified > :lastSyncTime " +
           "AND p.deleted = false ORDER BY p.gmtModified ASC")
    List<PosOrder> findOrdersForSync(@Param("lastSyncTime") Long lastSyncTime, Pageable pageable);

    /**
     * 检查订单是否已存在
     */
    boolean existsByBizOrderId(Long bizOrderId);

    /**
     * 批量查找订单
     */
    List<PosOrder> findByBizOrderIdIn(List<Long> bizOrderIds);
}
