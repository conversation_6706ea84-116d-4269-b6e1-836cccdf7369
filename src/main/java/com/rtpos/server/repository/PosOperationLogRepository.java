package com.rtpos.server.repository;

import com.rtpos.server.entity.PosOperationLog;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * POS收银操作日志数据访问层
 * 
 * <AUTHOR>
 */
@Repository
public interface PosOperationLogRepository extends JpaRepository<PosOperationLog, Long> {

    /**
     * 根据门店编号和POS机编号查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.storeNo = :storeNo AND p.posNo = :posNo " +
           "AND p.deleted = false ORDER BY p.occurrenceTime DESC")
    List<PosOperationLog> findByStoreNoAndPosNo(@Param("storeNo") Integer storeNo, 
                                                @Param("posNo") Integer posNo, 
                                                Pageable pageable);

    /**
     * 根据门店编号、POS机编号和时间范围查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.storeNo = :storeNo AND p.posNo = :posNo " +
           "AND p.occurrenceTime >= :beginTime AND p.occurrenceTime <= :endTime " +
           "AND p.deleted = false ORDER BY p.occurrenceTime DESC")
    List<PosOperationLog> findByStoreNoAndPosNoAndTimeRange(@Param("storeNo") Integer storeNo,
                                                            @Param("posNo") Integer posNo,
                                                            @Param("beginTime") Long beginTime,
                                                            @Param("endTime") Long endTime,
                                                            Pageable pageable);

    /**
     * 根据操作类型查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.operationType = :operationType " +
           "AND p.deleted = false ORDER BY p.occurrenceTime DESC")
    List<PosOperationLog> findByOperationType(@Param("operationType") Integer operationType, 
                                             Pageable pageable);

    /**
     * 根据流水ID查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.flowId = :flowId AND p.deleted = false")
    List<PosOperationLog> findByFlowId(@Param("flowId") String flowId);

    /**
     * 根据订单ID查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.orderId = :orderId AND p.deleted = false " +
           "ORDER BY p.occurrenceTime ASC")
    List<PosOperationLog> findByOrderId(@Param("orderId") String orderId);

    /**
     * 根据班次号查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.shiftNo = :shiftNo AND p.deleted = false " +
           "ORDER BY p.occurrenceTime ASC")
    List<PosOperationLog> findByShiftNo(@Param("shiftNo") String shiftNo);

    /**
     * 查找最新的操作日志（用于增量同步）
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.deleted = false ORDER BY p.occurrenceTime DESC")
    List<PosOperationLog> findLatestOperationLogs(Pageable pageable);

    /**
     * 根据修改时间查找需要同步的操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.updatedAt > :lastSyncTime " +
           "AND p.deleted = false ORDER BY p.updatedAt ASC")
    List<PosOperationLog> findOperationLogsForSync(@Param("lastSyncTime") LocalDateTime lastSyncTime, 
                                                   Pageable pageable);

    /**
     * 检查操作日志是否已存在（根据门店、POS机、发生时间和操作类型）
     */
    @Query("SELECT COUNT(p) > 0 FROM PosOperationLog p WHERE p.storeNo = :storeNo " +
           "AND p.posNo = :posNo AND p.occurrenceTime = :occurrenceTime " +
           "AND p.operationType = :operationType AND p.deleted = false")
    boolean existsByStoreNoAndPosNoAndOccurrenceTimeAndOperationType(@Param("storeNo") Integer storeNo,
                                                                     @Param("posNo") Integer posNo,
                                                                     @Param("occurrenceTime") Long occurrenceTime,
                                                                     @Param("operationType") Integer operationType);

    /**
     * 根据唯一标识查找操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.storeNo = :storeNo AND p.posNo = :posNo " +
           "AND p.occurrenceTime = :occurrenceTime AND p.operationType = :operationType " +
           "AND p.deleted = false")
    Optional<PosOperationLog> findByUniqueKey(@Param("storeNo") Integer storeNo,
                                              @Param("posNo") Integer posNo,
                                              @Param("occurrenceTime") Long occurrenceTime,
                                              @Param("operationType") Integer operationType);

    /**
     * 统计门店今日操作日志数量
     */
    @Query("SELECT COUNT(p) FROM PosOperationLog p WHERE p.storeNo = :storeNo " +
           "AND p.occurrenceTimeDateTime >= :startOfDay AND p.occurrenceTimeDateTime < :endOfDay " +
           "AND p.deleted = false")
    Long countTodayOperationLogsByStoreNo(@Param("storeNo") Integer storeNo,
                                         @Param("startOfDay") LocalDateTime startOfDay,
                                         @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * 根据同步状态查询操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.syncStatus = :syncStatus " +
           "AND p.deleted = false ORDER BY p.createdAt ASC")
    List<PosOperationLog> findBySyncStatus(@Param("syncStatus") Integer syncStatus, 
                                          Pageable pageable);

    /**
     * 批量查找操作日志
     */
    @Query("SELECT p FROM PosOperationLog p WHERE p.storeNo IN :storeNos AND p.posNo IN :posNos " +
           "AND p.deleted = false ORDER BY p.occurrenceTime DESC")
    List<PosOperationLog> findByStoreNosAndPosNos(@Param("storeNos") List<Integer> storeNos,
                                                  @Param("posNos") List<Integer> posNos,
                                                  Pageable pageable);
}
