package com.rtpos.server.schedule;

import com.rtpos.server.service.PosOperationLogSyncService;
import com.rtpos.server.service.PosStoreService;
import com.rtpos.server.service.PosStoreStatusService;
import com.rtpos.server.service.SyncStatusService;
import com.rtpos.server.entity.PosStore;
import com.rtpos.server.entity.SyncStatus;
import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.dto.PosStoreStatusBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * POS收银操作日志数据同步定时任务
 *
 * 优化策略：
 * 1. 根据门店信息获取在线POS设备
 * 2. 优先同步在线设备的收银员日志
 * 3. 分批次同步门店数据，避免一次性处理过多门店
 * 4. 时间分片同步，将大时间段拆分为小时间段
 * 5. 并发控制，避免过多并发请求
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "pos.sync.operation-log.enabled", havingValue = "true", matchIfMissing = true)
public class PosOperationLogSyncScheduler {

    private final PosOperationLogSyncService operationLogSyncService;
    private final PosStoreService posStoreService;
    private final PosStoreStatusService posStoreStatusService;
    private final SyncStatusService syncStatusService;

    // 线程池用于并发同步
    private final ExecutorService syncExecutor = Executors.newFixedThreadPool(3);

    @Value("${pos.sync.operation-log.batch-stores:5}")
    private int batchStoreSize;

    @Value("${pos.sync.operation-log.time-slice-hours:2}")
    private int timeSliceHours;

    @Value("${pos.sync.operation-log.max-concurrent-stores:2}")
    private int maxConcurrentStores;

    @Value("${pos.sync.operation-log.online-priority:true}")
    private boolean onlinePriority;

    /**
     * 增量同步任务 - 每5分钟执行一次
     * 优化策略：优先同步在线POS设备的收银员日志
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    @ConditionalOnProperty(name = "pos.sync.operation-log.incremental.enabled", havingValue = "true", matchIfMissing = true)
    public void incrementalSync() {
        log.info("Starting scheduled incremental sync for operation logs with online priority");
        try {
            if (onlinePriority) {
                incrementalSyncWithOnlinePriority();
            } else {
                operationLogSyncService.incrementalSyncOperationLogs();
            }
            log.info("Completed scheduled incremental sync for operation logs");
        } catch (Exception e) {
            log.error("Failed to execute scheduled incremental sync for operation logs", e);
        }
    }

    /**
     * 检查缺失操作日志任务 - 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    @ConditionalOnProperty(name = "pos.sync.operation-log.check-missing.enabled", havingValue = "true", matchIfMissing = false)
    public void checkMissingOperationLogs() {
        log.info("Starting scheduled check missing operation logs");
        try {
            operationLogSyncService.checkAndSyncMissingOperationLogs();
            log.info("Completed scheduled check missing operation logs");
        } catch (Exception e) {
            log.error("Failed to execute scheduled check missing operation logs", e);
        }
    }

    /**
     * 全量同步任务 - 每天凌晨3点执行（避免与订单同步冲突）
     * 优化策略：分批次同步所有门店，优先同步在线POS设备
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @ConditionalOnProperty(name = "pos.sync.operation-log.full.enabled", havingValue = "true", matchIfMissing = false)
    public void fullSync() {
        log.info("Starting scheduled full sync for operation logs with optimized strategy");
        try {
            // 同步前一天的数据
            long endTime = System.currentTimeMillis();
            long startTime = endTime - 24 * 60 * 60 * 1000L; // 24小时前

            // 分批次同步所有门店
            batchSyncAllStoresOperationLogs(startTime, endTime);

            log.info("Completed scheduled full sync for operation logs");
        } catch (Exception e) {
            log.error("Failed to execute scheduled full sync for operation logs", e);
        }
    }

    /**
     * 数据清理任务 - 每周日凌晨4点执行
     * 清理超过30天的操作日志数据（可配置）
     */
    @Scheduled(cron = "0 0 4 * * SUN")
    @ConditionalOnProperty(name = "pos.sync.operation-log.cleanup.enabled", havingValue = "true", matchIfMissing = false)
    public void cleanupOldOperationLogs() {
        log.info("Starting scheduled cleanup for old operation logs");
        try {
            // 这里可以实现清理逻辑
            // 例如：删除30天前的操作日志数据
            
            log.info("Completed scheduled cleanup for old operation logs");
        } catch (Exception e) {
            log.error("Failed to execute scheduled cleanup for old operation logs", e);
        }
    }

    /**
     * 健康检查任务 - 每小时执行一次
     * 检查同步服务的健康状态
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    @ConditionalOnProperty(name = "pos.sync.operation-log.health-check.enabled", havingValue = "true", matchIfMissing = false)
    public void healthCheck() {
        log.debug("Starting health check for operation log sync service");
        try {
            // 这里可以实现健康检查逻辑
            // 例如：检查API连接状态、数据库连接状态等

            log.debug("Completed health check for operation log sync service");
        } catch (Exception e) {
            log.error("Health check failed for operation log sync service", e);
        }
    }

    /**
     * 优化的增量同步方法 - 优先同步在线POS设备
     * 使用真正的增量策略，避免重复数据
     */
    private void incrementalSyncWithOnlinePriority() {
        log.info("Starting incremental sync with online priority strategy");

        try {
            long currentTime = System.currentTimeMillis();

            // 分批次获取门店并同步
            int pageNumber = 0;
            Page<PosStore> storePage;
            int totalProcessed = 0;

            do {
                // 分页获取门店数据
                storePage = posStoreService.getAllStores(PageRequest.of(pageNumber, batchStoreSize));
                List<PosStore> stores = storePage.getContent();

                if (stores.isEmpty()) {
                    break;
                }

                log.info("Processing incremental sync batch {}, stores: {}", pageNumber + 1, stores.size());

                // 同步当前批次的门店（使用真正的增量策略）
                totalProcessed += syncStoresBatchWithIncrementalStrategy(stores, currentTime);

                pageNumber++;

                // 批次间添加延迟
                Thread.sleep(1000);

            } while (storePage.hasNext());

            log.info("Incremental sync with online priority completed. Total processed: {}", totalProcessed);

        } catch (Exception e) {
            log.error("Failed to execute incremental sync with online priority", e);
        }
    }

    /**
     * 分批次同步所有门店的操作日志数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void batchSyncAllStoresOperationLogs(long startTime, long endTime) {
        log.info("Starting batch sync for all stores operation logs, timeRange: {} - {}", startTime, endTime);

        try {
            // 获取所有门店数据
            int pageNumber = 0;
            Page<PosStore> storePage;
            int totalStores = 0;
            int processedStores = 0;

            do {
                // 分页获取门店数据
                storePage = posStoreService.getAllStores(PageRequest.of(pageNumber, batchStoreSize));
                List<PosStore> stores = storePage.getContent();

                if (stores.isEmpty()) {
                    break;
                }

                totalStores += stores.size();
                log.info("Processing operation logs batch {}, stores: {}", pageNumber + 1, stores.size());

                // 分时间段同步当前批次的门店
                processedStores += syncStoresBatchWithTimeSlicing(stores, startTime, endTime);

                pageNumber++;

                // 批次间添加延迟，避免对外部API造成过大压力
                Thread.sleep(2000);

            } while (storePage.hasNext());

            log.info("Batch sync for operation logs completed. Total stores: {}, Processed: {}", totalStores, processedStores);

        } catch (Exception e) {
            log.error("Failed to execute batch sync for all stores operation logs", e);
        }
    }

    /**
     * 使用真正增量策略同步门店操作日志，避免重复数据
     *
     * @param stores 门店列表
     * @param currentTime 当前时间
     * @return 成功处理的门店数量
     */
    private int syncStoresBatchWithIncrementalStrategy(List<PosStore> stores, long currentTime) {
        log.info("Syncing {} stores operation logs with incremental strategy", stores.size());

        int successCount = 0;
        String syncType = SyncStatus.Type.OPERATION_LOG_INCREMENTAL.name();

        for (PosStore store : stores) {
            try {
                // 获取该门店上次成功同步的时间
                Long lastSyncTime = syncStatusService.getLastSyncTime(syncType, store.getStoreId());

                // 如果没有同步记录，从24小时前开始同步
                if (lastSyncTime == null) {
                    lastSyncTime = currentTime - 24 * 60 * 60 * 1000L; // 24小时前
                    log.info("Store {} has no sync history, starting from 24 hours ago", store.getStoreId());
                } else {
                    log.debug("Store {} last sync time: {}", store.getStoreId(), lastSyncTime);
                }

                // 计算增量同步的时间范围
                long startTime = lastSyncTime;
                long endTime = currentTime;

                // 如果时间范围太小（小于1分钟），跳过此次同步
                if (endTime - startTime < 60 * 1000L) {
                    log.debug("Store {} sync interval too small, skipping", store.getStoreId());
                    continue;
                }

                // 记录同步开始
                syncStatusService.recordSyncStart(syncType, store.getStoreId(), startTime, endTime);

                // 获取门店的在线POS设备状态并同步
                int syncedDeviceCount = syncStoreWithIncrementalStrategy(store, startTime, endTime);

                // 记录同步成功
                syncStatusService.recordSyncSuccess(syncType, store.getStoreId(), syncedDeviceCount);

                successCount++;

                log.info("Store {} incremental sync completed, synced {} devices",
                        store.getStoreId(), syncedDeviceCount);

            } catch (Exception e) {
                log.error("Failed to sync operation logs for store: {}", store.getStoreId(), e);

                // 记录同步失败
                syncStatusService.recordSyncFailure(syncType, store.getStoreId(), e.getMessage());
            }
        }

        return successCount;
    }

    /**
     * 使用增量策略同步单个门店的操作日志
     *
     * @param store 门店信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 同步的设备数量
     */
    private int syncStoreWithIncrementalStrategy(PosStore store, long startTime, long endTime) {
        int syncedDeviceCount = 0;

        try {
            // 获取门店的在线POS设备状态
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.valueOf(store.getStoreId()));

            PosStoreStatusResponse statusResponse = posStoreStatusService.getStoreStatus(statusRequest);

            if (statusResponse != null && statusResponse.isSuccess() && statusResponse.getBody() != null) {
                PosStoreStatusBody statusBody = statusResponse.getBody();

                // 优先同步在线设备
                if (statusBody.getOnline() != null && !statusBody.getOnline().isEmpty()) {
                    log.debug("Store {} has {} online POS devices for incremental sync",
                            store.getStoreId(), statusBody.getOnline().size());

                    for (Integer posNo : statusBody.getOnline()) {
                        try {
                            operationLogSyncService.fullSyncOperationLogs(
                                Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);
                            syncedDeviceCount++;

                            // 在线设备同步间隔较短
                            Thread.sleep(300);
                        } catch (Exception e) {
                            log.error("Failed to sync operation logs for store: {}, online pos: {}",
                                    store.getStoreId(), posNo, e);
                        }
                    }
                }

                // 如果在线设备较少，适量同步离线设备
                if (statusBody.getOffline() != null && !statusBody.getOffline().isEmpty()) {
                    int onlineCount = statusBody.getOnline() != null ? statusBody.getOnline().size() : 0;

                    // 如果在线设备少于2个，同步部分离线设备
                    if (onlineCount < 2) {
                        int maxOfflineSync = Math.min(statusBody.getOffline().size(), 2 - onlineCount);
                        log.debug("Store {} syncing {} offline devices to supplement online devices",
                                store.getStoreId(), maxOfflineSync);

                        for (int i = 0; i < maxOfflineSync; i++) {
                            Integer posNo = statusBody.getOffline().get(i);
                            try {
                                operationLogSyncService.fullSyncOperationLogs(
                                    Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);
                                syncedDeviceCount++;

                                // 离线设备同步间隔较长
                                Thread.sleep(500);
                            } catch (Exception e) {
                                log.error("Failed to sync operation logs for store: {}, offline pos: {}",
                                        store.getStoreId(), posNo, e);
                            }
                        }
                    }
                }
            } else {
                log.warn("Failed to get store status for store: {}, using fallback incremental sync",
                        store.getStoreId());

                // 降级策略：如果无法获取设备状态，同步默认设备
                // 这里可以配置一些默认的POS设备号进行同步
                syncedDeviceCount = syncStoreWithFallbackStrategy(store, startTime, endTime);
            }

        } catch (Exception e) {
            log.error("Failed to sync operation logs with incremental strategy for store: {}",
                    store.getStoreId(), e);
        }

        return syncedDeviceCount;
    }

    /**
     * 降级策略：当无法获取设备状态时的同步方法
     *
     * @param store 门店信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 同步的设备数量
     */
    private int syncStoreWithFallbackStrategy(PosStore store, long startTime, long endTime) {
        int syncedDeviceCount = 0;

        // 使用配置的默认POS设备号进行同步
        // 这里可以从配置文件读取或使用常见的POS设备号
        List<Integer> defaultPosNos = getDefaultPosNos();

        log.info("Using fallback strategy for store {}, syncing {} default devices",
                store.getStoreId(), defaultPosNos.size());

        for (Integer posNo : defaultPosNos) {
            try {
                operationLogSyncService.fullSyncOperationLogs(
                    Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);
                syncedDeviceCount++;

                // 降级策略使用较长的延迟
                Thread.sleep(1000);
            } catch (Exception e) {
                log.error("Failed to sync operation logs for store: {}, fallback pos: {}",
                        store.getStoreId(), posNo, e);
            }
        }

        return syncedDeviceCount;
    }

    /**
     * 获取默认的POS设备号列表
     */
    @Value("${pos.sync.operation-log.default-pos-nos:1,2,3}")
    private String defaultPosNosConfig;

    private List<Integer> getDefaultPosNos() {
        List<Integer> defaultPosNos = new ArrayList<>();

        if (defaultPosNosConfig != null && !defaultPosNosConfig.trim().isEmpty()) {
            String[] posNos = defaultPosNosConfig.split(",");
            for (String posNo : posNos) {
                try {
                    defaultPosNos.add(Integer.valueOf(posNo.trim()));
                } catch (NumberFormatException e) {
                    log.warn("Invalid default POS number: {}", posNo);
                }
            }
        }

        // 如果配置为空，使用默认值
        if (defaultPosNos.isEmpty()) {
            defaultPosNos.addAll(List.of(1, 2, 3));
        }

        return defaultPosNos;
    }

    /**
     * 同步一批门店的操作日志，优先同步在线POS设备
     *
     * @param stores 门店列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功处理的门店数量
     */
    private int syncStoresBatchWithOnlinePriority(List<PosStore> stores, long startTime, long endTime) {
        log.info("Syncing {} stores operation logs with online priority", stores.size());

        int successCount = 0;

        for (PosStore store : stores) {
            try {
                // 获取门店的在线POS设备状态
                PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
                statusRequest.setStoreNo(Integer.valueOf(store.getStoreId()));

                PosStoreStatusResponse statusResponse = posStoreStatusService.getStoreStatus(statusRequest);

                if (statusResponse != null && statusResponse.isSuccess() && statusResponse.getBody() != null) {
                    PosStoreStatusBody statusBody = statusResponse.getBody();

                    // 优先同步在线设备
                    if (statusBody.getOnline() != null && !statusBody.getOnline().isEmpty()) {
                        log.info("Store {} has {} online POS devices, syncing operation logs",
                                store.getStoreId(), statusBody.getOnline().size());

                        for (Integer posNo : statusBody.getOnline()) {
                            try {
                                operationLogSyncService.fullSyncOperationLogs(
                                    Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);

                                // 添加延迟避免API调用过于频繁
                                Thread.sleep(500);
                            } catch (Exception e) {
                                log.error("Failed to sync operation logs for store: {}, online pos: {}",
                                        store.getStoreId(), posNo, e);
                            }
                        }
                    }

                    // 如果配置允许，也同步离线设备（优先级较低）
                    if (statusBody.getOffline() != null && !statusBody.getOffline().isEmpty()) {
                        log.debug("Store {} has {} offline POS devices, syncing with lower priority",
                                store.getStoreId(), statusBody.getOffline().size());

                        // 只同步部分离线设备，避免过多API调用
                        int maxOfflineSync = Math.min(statusBody.getOffline().size(), 3);
                        for (int i = 0; i < maxOfflineSync; i++) {
                            Integer posNo = statusBody.getOffline().get(i);
                            try {
                                operationLogSyncService.fullSyncOperationLogs(
                                    Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);

                                Thread.sleep(1000); // 离线设备同步间隔更长
                            } catch (Exception e) {
                                log.error("Failed to sync operation logs for store: {}, offline pos: {}",
                                        store.getStoreId(), posNo, e);
                            }
                        }
                    }

                    successCount++;
                } else {
                    log.warn("Failed to get store status for store: {}, skipping operation log sync",
                            store.getStoreId());
                }

            } catch (Exception e) {
                log.error("Failed to sync operation logs for store: {}", store.getStoreId(), e);
            }
        }

        return successCount;
    }

    /**
     * 同步一批门店的操作日志，使用时间分片策略
     *
     * @param stores 门店列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功处理的门店数量
     */
    private int syncStoresBatchWithTimeSlicing(List<PosStore> stores, long startTime, long endTime) {
        log.info("Syncing {} stores operation logs with time slicing", stores.size());

        int successCount = 0;

        // 计算时间分片
        long timeSliceMillis = timeSliceHours * 60 * 60 * 1000L;

        for (long currentStart = startTime; currentStart < endTime; currentStart += timeSliceMillis) {
            long currentEnd = Math.min(currentStart + timeSliceMillis, endTime);

            log.info("Processing operation logs time slice: {} - {}", currentStart, currentEnd);

            // 并发同步当前时间片的门店数据
            successCount += syncStoresConcurrentlyWithOnlinePriority(stores, currentStart, currentEnd);

            // 时间片间添加延迟
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Time slice sync interrupted");
                break;
            }
        }

        return successCount;
    }

    /**
     * 并发同步门店操作日志数据，优先处理在线POS设备
     *
     * @param stores 门店列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功处理的门店数量
     */
    private int syncStoresConcurrentlyWithOnlinePriority(List<PosStore> stores, long startTime, long endTime) {
        int successCount = 0;

        // 将门店分组，每组最多maxConcurrentStores个门店并发处理
        for (int i = 0; i < stores.size(); i += maxConcurrentStores) {
            int endIndex = Math.min(i + maxConcurrentStores, stores.size());
            List<PosStore> storeGroup = stores.subList(i, endIndex);

            // 创建并发任务
            CompletableFuture<?>[] futures = storeGroup.stream()
                .map(store -> CompletableFuture.runAsync(() -> {
                    try {
                        log.debug("Syncing operation logs for store: {} for time range: {} - {}",
                                store.getStoreId(), startTime, endTime);

                        // 获取门店状态并优先同步在线设备
                        syncStoreOperationLogsWithOnlinePriority(store, startTime, endTime);

                        log.debug("Successfully synced operation logs for store: {}", store.getStoreId());
                    } catch (Exception e) {
                        log.error("Failed to sync operation logs for store: {}", store.getStoreId(), e);
                    }
                }, syncExecutor))
                .toArray(CompletableFuture[]::new);

            // 等待当前组的所有任务完成
            try {
                CompletableFuture.allOf(futures).get(30, TimeUnit.MINUTES);
                successCount += storeGroup.size();
            } catch (Exception e) {
                log.error("Failed to complete concurrent sync for operation logs store group", e);
            }

            // 组间添加延迟
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Concurrent operation logs sync interrupted");
                break;
            }
        }

        return successCount;
    }

    /**
     * 同步单个门店的操作日志，优先处理在线POS设备
     *
     * @param store 门店信息
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private void syncStoreOperationLogsWithOnlinePriority(PosStore store, long startTime, long endTime) {
        try {
            // 获取门店的在线POS设备状态
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.valueOf(store.getStoreId()));

            PosStoreStatusResponse statusResponse = posStoreStatusService.getStoreStatus(statusRequest);

            if (statusResponse != null && statusResponse.isSuccess() && statusResponse.getBody() != null) {
                PosStoreStatusBody statusBody = statusResponse.getBody();

                // 优先同步在线设备
                if (statusBody.getOnline() != null && !statusBody.getOnline().isEmpty()) {
                    log.debug("Store {} has {} online POS devices",
                            store.getStoreId(), statusBody.getOnline().size());

                    for (Integer posNo : statusBody.getOnline()) {
                        try {
                            operationLogSyncService.fullSyncOperationLogs(
                                Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);
                        } catch (Exception e) {
                            log.error("Failed to sync operation logs for store: {}, online pos: {}",
                                    store.getStoreId(), posNo, e);
                        }
                    }
                }

                // 如果在线设备较少，也同步部分离线设备
                if (statusBody.getOffline() != null && !statusBody.getOffline().isEmpty()) {
                    int onlineCount = statusBody.getOnline() != null ? statusBody.getOnline().size() : 0;

                    // 如果在线设备少于3个，同步部分离线设备
                    if (onlineCount < 3) {
                        int maxOfflineSync = Math.min(statusBody.getOffline().size(), 3 - onlineCount);
                        for (int i = 0; i < maxOfflineSync; i++) {
                            Integer posNo = statusBody.getOffline().get(i);
                            try {
                                operationLogSyncService.fullSyncOperationLogs(
                                    Integer.valueOf(store.getStoreId()), posNo, startTime, endTime);
                            } catch (Exception e) {
                                log.error("Failed to sync operation logs for store: {}, offline pos: {}",
                                        store.getStoreId(), posNo, e);
                            }
                        }
                    }
                }
            } else {
                log.warn("Failed to get store status for store: {}, using fallback sync strategy",
                        store.getStoreId());

                // 降级策略：如果无法获取设备状态，使用默认的同步方式
                // 这里可以配置一些默认的POS设备号进行同步
            }

        } catch (Exception e) {
            log.error("Failed to sync operation logs with online priority for store: {}", store.getStoreId(), e);
        }
    }

    /**
     * 应用关闭时清理线程池
     */
    public void destroy() {
        if (syncExecutor != null && !syncExecutor.isShutdown()) {
            syncExecutor.shutdown();
            try {
                if (!syncExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    syncExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                syncExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
