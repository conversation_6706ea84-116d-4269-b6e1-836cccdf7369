package com.rtpos.server.controller;

import com.rtpos.server.dto.ApiResponse;
import com.rtpos.server.dto.DashboardRequest;
import com.rtpos.server.dto.DashboardResponse;
import com.rtpos.server.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * Dashboard数据控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/pos/dashboard")
@RequiredArgsConstructor
@Validated
@Tag(name = "Dashboard管理", description = "POS看盘Dashboard相关API")
public class DashboardController {

    private final DashboardService dashboardService;

    @Operation(summary = "获取Dashboard整合数据", description = "获取指定门店的Dashboard整合数据，包括设备状态、订单分布、使用率等")
    @PostMapping("/data")
    public ResponseEntity<ApiResponse<DashboardResponse>> getDashboardData(
            @Parameter(description = "Dashboard数据查询请求") 
            @Valid @RequestBody DashboardRequest request) {
        
        log.info("Getting dashboard data for store: {}, date: {}", request.getStoreId(), request.getDate());
        
        try {
            DashboardResponse data = dashboardService.getDashboardData(request);
            return ResponseEntity.ok(ApiResponse.success(data));
            
        } catch (Exception e) {
            log.error("Failed to get dashboard data for store: {}", request.getStoreId(), e);
            return ResponseEntity.ok(ApiResponse.error("获取Dashboard数据失败: " + e.getMessage()));
        }
    }
}
