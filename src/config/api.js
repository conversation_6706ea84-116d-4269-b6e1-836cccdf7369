// API配置文件
const config = {
  // 基础URL
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080',
  
  // API前缀
  apiPrefix: process.env.VUE_APP_API_PREFIX || '/api',
  
  // 超时时间
  timeout: 10000,
  
  // 是否使用mock数据
  useMock: process.env.VUE_APP_USE_MOCK === 'true',
  
  // 环境标识
  env: process.env.VUE_APP_ENV || 'development'
}

// API端点配置
export const API_ENDPOINTS = {
  // 门店相关
  POS_STORE: '/stores/sync',

  // 门店POS状态查询
  POS_STATUS: '/api/pos/store-status/query',
  
  // 仪表盘相关
  DASHBOARD_DATA: '/dashboard/data',
  
  // 设备相关
  DEVICE_STATUS: '/device/status',
  
  // 能耗相关
  ENERGY_CONSUMPTION: '/energy/consumption'
}

// 完整的API URL构建函数
export const buildApiUrl = (endpoint) => {
  return `${config.baseURL}${config.apiPrefix}${endpoint}`
}

// 导出配置
export default config

// 打印当前环境配置（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 API配置:', {
    环境: config.env,
    基础URL: config.baseURL,
    API前缀: config.apiPrefix,
    使用Mock: config.useMock,
    超时时间: config.timeout + 'ms'
  })
}
